<script>
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import AudioPlayer from '$lib/components/AudioPlayer.svelte';

	// Props from page data
	export let data;

	// Extract article from data
	$: article = data.article;

	// Debug logging
	$: {
		console.log('Article data:', article);
		console.log('Audio URL:', article?.audioUrl);
		console.log('Audio Title:', article?.audioTitle);
	}

	/**
	 * Format date for display
	 * @param {string} dateString - ISO date string
	 * @returns {string} Formatted date
	 */
	function formatDate(dateString) {
		const date = new Date(dateString);
		return date.toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'long',
			day: 'numeric'
		});
	}

	/**
	 * Navigate back to homepage
	 */
	function goBack() {
		goto('/');
	}
</script>

<svelte:head>
	<title>{article.title} - FWFC News</title>
	<meta name="description" content={article.content.replace(/<[^>]*>/g, '').substring(0, 160)} />
	<meta property="og:title" content={article.title} />
	<meta property="og:description" content={article.content.replace(/<[^>]*>/g, '').substring(0, 160)} />
	{#if article.imageUrl}
		<meta property="og:image" content={article.imageUrl} />
	{/if}
	<meta property="og:type" content="article" />
	<meta property="og:url" content="{$page.url.origin}/news/{article.id}" />
</svelte:head>

<div class="article-container">
	<!-- Navigation -->
	<nav class="article-nav">
		<button class="back-button" on:click={goBack}>
			← Back to Home
		</button>
	</nav>

	<!-- Article Header -->
	<header class="article-header">
		<h1 class="article-title">{article.title}</h1>
		<div class="article-meta">
			<time class="article-date" datetime={article.createdAt}>
				Published on {formatDate(article.createdAt)}
			</time>
			{#if article.updatedAt && article.updatedAt !== article.createdAt}
				<span class="article-updated">
					(Updated {formatDate(article.updatedAt)})
				</span>
			{/if}
		</div>
	</header>

	<!-- Featured Image -->
	{#if article.imageUrl}
		<div class="article-image">
			<img src={article.imageUrl} alt={article.title} />
		</div>
	{/if}

	<!-- Article Content -->
	<main class="article-content">
		{@html article.content}
	</main>

	<!-- Audio Player -->
	{#if article.audioUrl}
		<div class="audio-section">
			<h3>Audio Content</h3>
			<p>Audio URL: {article.audioUrl}</p>
			<p>Audio Title: {article.audioTitle}</p>
			<AudioPlayer
				audioUrl={article.audioUrl}
				audioTitle={article.audioTitle || `${article.title} - Audio`}
				audioDuration={article.audioDuration}
				showDownload={true}
			/>
		</div>
	{:else}
		<p>No audio content available</p>
	{/if}

	<!-- Article Footer -->
	<footer class="article-footer">
		<div class="article-actions">
			<button class="back-button secondary" on:click={goBack}>
				← Back to Home
			</button>
		</div>
	</footer>
</div>

<style>
	.article-container {
		max-width: 800px;
		margin: 0 auto;
		padding: 2rem;
		line-height: 1.6;
		background-color: var(--color-bg-primary);
		color: var(--color-text-primary);
	}

	.article-nav {
		margin-bottom: 2rem;
	}

	.back-button {
		display: inline-flex;
		align-items: center;
		padding: 0.75rem 1.5rem;
		background-color: var(--color-interactive-primary);
		color: var(--color-text-inverse);
		border: none;
		border-radius: 4px;
		font-size: 1rem;
		cursor: pointer;
		text-decoration: none;
		transition: background-color 0.2s;
	}

	.back-button:hover {
		background-color: var(--color-interactive-primary-hover);
	}

	.back-button.secondary {
		background-color: var(--color-surface-secondary);
		color: var(--color-text-primary);
		border: 1px solid var(--color-border-primary);
	}

	.back-button.secondary:hover {
		background-color: var(--color-surface-tertiary);
	}

	.article-header {
		margin-bottom: 2rem;
		text-align: center;
	}

	.article-title {
		font-size: 2.5rem;
		font-weight: bold;
		margin: 0 0 1rem 0;
		color: var(--color-text-primary);
		line-height: 1.2;
	}

	.article-meta {
		color: var(--color-text-secondary);
		font-size: 1rem;
	}

	.article-date {
		font-weight: 500;
	}

	.article-updated {
		font-style: italic;
		margin-left: 0.5rem;
	}

	.article-image {
		margin: 2rem 0;
		text-align: center;
	}

	.article-image img {
		max-width: 100%;
		height: auto;
		border-radius: 8px;
		box-shadow: 0 4px 8px var(--color-shadow-primary);
	}

	.article-content {
		margin: 2rem 0;
		font-size: 1.1rem;
		color: var(--color-text-primary);
	}

	/* Style the HTML content from the rich text editor */
	.article-content :global(p) {
		margin: 1rem 0;
		color: var(--color-text-primary);
	}

	.article-content :global(h1) {
		font-size: 2rem;
		margin: 2rem 0 1rem 0;
		color: var(--color-text-primary);
	}

	.article-content :global(h2) {
		font-size: 1.75rem;
		margin: 1.5rem 0 1rem 0;
		color: var(--color-text-primary);
	}

	.article-content :global(h3) {
		font-size: 1.5rem;
		margin: 1.5rem 0 1rem 0;
		color: var(--color-text-primary);
	}

	.article-content :global(ul),
	.article-content :global(ol) {
		margin: 1rem 0;
		padding-left: 2rem;
		color: var(--color-text-primary);
	}

	.article-content :global(li) {
		margin: 0.5rem 0;
		color: var(--color-text-primary);
	}

	.article-content :global(blockquote) {
		margin: 1.5rem 0;
		padding: 1rem 1.5rem;
		border-left: 4px solid var(--color-interactive-primary);
		background-color: var(--color-surface-secondary);
		font-style: italic;
		color: var(--color-text-primary);
	}

	.article-content :global(a) {
		color: var(--color-interactive-primary);
		text-decoration: underline;
	}

	.article-content :global(a:hover) {
		color: var(--color-interactive-primary-hover);
	}

	.article-content :global(strong) {
		font-weight: bold;
		color: var(--color-text-primary);
	}

	.article-content :global(em) {
		font-style: italic;
		color: var(--color-text-primary);
	}

	.article-content :global(code) {
		background-color: var(--color-surface-secondary);
		color: var(--color-text-primary);
		padding: 0.2rem 0.4rem;
		border-radius: 3px;
		font-family: 'Courier New', monospace;
		font-size: 0.9rem;
	}

	.article-content :global(pre) {
		background-color: var(--color-surface-secondary);
		color: var(--color-text-primary);
		padding: 1rem;
		border-radius: 4px;
		overflow-x: auto;
		margin: 1rem 0;
	}

	.article-content :global(table) {
		width: 100%;
		border-collapse: collapse;
		margin: 1rem 0;
		color: var(--color-text-primary);
	}

	.article-content :global(th),
	.article-content :global(td) {
		border: 1px solid var(--color-border-primary);
		padding: 0.75rem;
		text-align: left;
		color: var(--color-text-primary);
	}

	.article-content :global(th) {
		background-color: var(--color-surface-secondary);
		font-weight: bold;
		color: var(--color-text-primary);
	}

	.article-content :global(img) {
		max-width: 100%;
		height: auto;
		border-radius: 4px;
		margin: 1rem 0;
	}

	.article-footer {
		margin-top: 3rem;
		padding-top: 2rem;
		border-top: 1px solid var(--color-border-primary);
		text-align: center;
	}

	.article-actions {
		display: flex;
		justify-content: center;
		gap: 1rem;
	}

	/* Responsive design */
	@media (max-width: 768px) {
		.article-container {
			padding: 1rem;
		}

		.article-title {
			font-size: 2rem;
		}

		.article-content {
			font-size: 1rem;
		}

		.back-button {
			padding: 0.5rem 1rem;
			font-size: 0.9rem;
		}
	}

	@media (max-width: 480px) {
		.article-title {
			font-size: 1.75rem;
		}

		.article-actions {
			flex-direction: column;
			align-items: center;
		}
	}
</style>
