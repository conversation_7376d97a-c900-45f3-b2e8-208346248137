{"level":"debug","message":"No session cookie found","timestamp":"2025-05-16 11:10:50:1050"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-05-16 11:10:50:1050"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-05-16 11:11:03:113"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-05-16 11:11:03:113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:04:114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:08:118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:08:118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:10:1110"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:13:1113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:14:1114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:47:1447"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:48:1448"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:48:1448"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:48:1448"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:49:1449"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:49:1449"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:49:1449"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:49:1449"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:50:1450"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:50:1450"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:51:1451"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:00:150"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:05:155"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-05-16 11:15:05:155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:05:155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:08:158"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:08:158"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:12:1512"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:12:1512"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:12:1512"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:12:1512"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:14:1514"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:15:1515"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:17:1517"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:17:1517"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:18:1518"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:20:1520"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:20:1520"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:21:1521"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:22:1522"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:41:1641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:41:1641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:43:1643"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:44:1644"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:44:1644"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:44:1644"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:51:1651"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-05-16 11:16:51:1651"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:51:1651"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:54:1654"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:57:1657"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:01:171"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:04:174"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:04:174"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:06:176"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:06:176"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:06:176"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:06:176"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:07:177"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:20:30:2030"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:20:52:2052"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:20:57:2057"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:04:214"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:12:2112"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-05-16 11:21:12:2112"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:12:2112"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:14:2114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:16:2116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:18:2118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:18:2118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:18:2118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:20:2120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:20:2120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:22:2122"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:23:2123"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:24:2124"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:41:2141"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:52:2152"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:52:2152"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:54:2154"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:54:2154"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:58:2158"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:31:2231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:31:2231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:38:2238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:38:2238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:38:2238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:40:2240"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:40:2240"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:41:2241"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:41:2241"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:42:2242"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:43:2243"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:44:2244"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:44:2244"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:46:2246"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:46:2246"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:47:2247"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:48:2248"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:55:2255"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:55:2255"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:57:2257"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:57:2257"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:00:230"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:01:231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:01:231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:18:2318"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:20:2320"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:50:2350"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:51:2351"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:51:2351"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:53:2353"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:54:2354"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:54:2354"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:55:2355"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:07:247"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:07:247"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:09:249"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:09:249"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:28:2428"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:28:2428"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:33:2433"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:34:2434"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:35:2435"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:25:18:2518"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:25:37:2537"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:25:38:2538"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-05-29 14:31:58:3158"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-05-29 14:32:06:326"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-05-29 14:33:18:3318"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-05-29 14:33:18:3318"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 14:33:18:3318"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 14:33:22:3322"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 14:51:22:5122"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 14:51:25:5125"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 14:56:07:567"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:22:322"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:29:329"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:44:344"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:48:348"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:49:349"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:53:353"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:05:43:543"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:05:45:545"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:06:51:651"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:06:54:654"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:01:71"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:03:73"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:05:75"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:39:739"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:39:739"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:41:741"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:43:743"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:43:743"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:45:745"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:50:750"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:50:750"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:51:751"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:53:753"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:53:753"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:02:102"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:05:105"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:14:1014"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:47:1047"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:47:1047"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:49:1049"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:49:1049"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:11:08:118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:11:23:1123"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:11:29:1129"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:27:1427"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:28:1428"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:28:1428"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:38:1438"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:38:1438"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:38:1438"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:52:1452"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:52:1452"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:52:1452"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:15:06:156"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:15:06:156"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:15:06:156"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:06:166"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:06:166"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:07:167"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:51:1651"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:51:1651"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:58:1658"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:58:1658"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:59:1659"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:59:1659"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:00:170"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:03:173"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:03:173"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:03:173"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:12:1712"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:18:1718"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:18:57:1857"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:03:193"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:03:193"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:06:196"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:06:196"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:06:196"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:11:1911"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:29:1929"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:32:1932"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:32:1932"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:33:1933"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:34:1934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:35:1935"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:35:1935"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:36:1936"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:37:1937"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:37:1937"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:38:1938"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:39:1939"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:39:1939"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:42:1942"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:42:1942"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:42:1942"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:43:1943"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:44:1944"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:47:1947"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:48:1948"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:49:1949"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:49:1949"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:50:1950"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:53:1953"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:56:1956"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:59:1959"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:01:201"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:04:204"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:07:207"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:09:209"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:28:2028"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:30:2030"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:34:2034"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:24:12:2412"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:24:22:2422"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:24:34:2434"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:24:46:2446"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:24:58:2458"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:25:33:2533"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:25:38:2538"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:06:266"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:12:2612"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:27:2627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:28:2628"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:34:2634"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:41:2641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:41:2641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:41:2641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:44:2644"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:01:271"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:02:272"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:03:273"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:03:273"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:03:273"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:11:2711"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:13:2713"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:13:2713"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:13:2713"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:15:2715"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:19:3119"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:27:3127"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:27:3127"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:27:3127"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:28:3128"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:31:3131"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:32:3132"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:32:3132"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:35:3135"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:39:3139"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:40:3140"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:40:3140"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:18:3318"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:21:3321"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:22:3322"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:22:3322"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:37:3337"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:42:3342"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:45:3345"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:45:3345"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:45:3345"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:34:29:3429"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:34:52:3452"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:23:3523"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:23:3523"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:23:3523"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:58:3558"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:58:3558"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:58:3558"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:14:3614"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:14:3614"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:14:3614"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:28:3628"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:31:3631"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:32:3632"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:32:3632"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:32:3632"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:19:3819"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:22:3822"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:22:3822"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:22:3822"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:40:3840"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:41:3841"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:41:3841"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:41:3841"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:42:3842"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:44:3844"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:44:3844"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:44:3844"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:48:3848"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:49:3849"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:49:3849"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:49:3849"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:39:50:3950"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:39:55:3955"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:40:26:4026"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:40:26:4026"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:40:27:4027"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:40:27:4027"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:40:27:4027"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:40:27:4027"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:40:48:4048"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:41:39:4139"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:41:52:4152"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:41:56:4156"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:41:56:4156"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:41:56:4156"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:42:16:4216"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:42:30:4230"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-03 16:42:30:4230"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:16:4316"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:16:4316"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:16:4316"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:17:4317"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:17:4317"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:20:4320"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:20:4320"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:20:4320"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:20:4320"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:20:4320"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:20:4320"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:38:4338"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:38:4338"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:45:10:4510"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:46:27:4627"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-03 16:46:27:4627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:46:27:4627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:46:32:4632"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:46:32:4632"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:46:33:4633"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:46:37:4637"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:46:37:4637"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:48:50:4850"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:48:56:4856"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:48:57:4857"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:49:11:4911"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:49:13:4913"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:49:14:4914"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:49:36:4936"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:49:37:4937"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:49:38:4938"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:52:01:521"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:52:06:526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:52:07:527"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:53:46:5346"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:55:37:5537"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:55:46:5546"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:55:46:5546"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:58:44:5844"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:58:44:5844"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:58:44:5844"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:58:49:5849"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:58:53:5853"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:58:53:5853"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:58:53:5853"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:59:07:597"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:59:23:5923"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:59:23:5923"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:59:23:5923"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:20:020"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:20:020"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:20:020"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:21:021"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:22:022"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:22:022"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:23:023"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:23:023"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:24:024"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:24:024"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:24:024"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:27:027"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:27:027"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:27:027"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:29:029"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:29:029"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:29:029"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:15:115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:15:115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:15:115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:16:116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:16:116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:17:117"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:17:117"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:18:118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:20:120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:20:120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:20:120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:20:120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:20:120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:20:120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:21:121"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:21:121"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:21:121"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:31:231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:31:231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:32:232"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:32:232"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:32:232"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:33:233"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:33:233"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:34:234"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:34:234"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:35:235"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:35:235"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:35:235"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:37:237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:37:237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:37:237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:37:237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:37:237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:37:237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:38:238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:38:238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:38:238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:08:58"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:08:58"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:09:59"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:09:59"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:09:59"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:10:510"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:10:510"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:11:511"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:46:546"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:54:554"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:06:07:67"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:13:03:133"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:13:08:138"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:13:08:138"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:13:08:138"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:13:18:1318"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:13:19:1319"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:13:20:1320"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:26:1726"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-03 17:17:26:1726"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:26:1726"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:28:1728"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:29:1729"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:29:1729"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:30:1730"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:31:1731"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:32:1732"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:32:1732"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:33:1733"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:42:1742"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:45:1745"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:46:1746"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:52:1752"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:18:09:189"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:18:12:1812"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:18:15:1815"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:18:15:1815"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:18:15:1815"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:19:38:1938"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:26:46:2646"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:12:3012"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:17:3017"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:29:3029"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-03 17:30:29:3029"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:29:3029"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:32:3032"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:33:3033"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:34:3034"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:39:3039"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:31:55:3155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:31:55:3155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:31:57:3157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:31:57:3157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:01:321"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:01:321"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:01:321"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:05:325"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:07:327"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:07:327"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:07:327"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:08:328"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:08:328"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:09:329"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:10:3210"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:11:3211"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:12:3212"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:12:3212"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:18:3218"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:22:3222"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:26:3226"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:27:3227"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:32:3232"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:35:3235"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:38:3238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:43:3243"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:43:3243"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:43:3243"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:06:336"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:49:3349"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:52:3352"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:54:3354"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:55:3355"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:55:3355"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:58:3358"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:58:3358"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:36:10:3610"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:36:46:3646"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:37:20:3720"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:37:24:3724"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:37:26:3726"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:37:27:3727"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:37:59:3759"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:37:59:3759"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:37:59:3759"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:38:47:3847"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:38:51:3851"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:38:54:3854"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:25:3925"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:29:3929"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:34:3934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:34:3934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:34:3934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:40:3940"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:47:3947"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:49:3949"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:59:3959"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:40:05:405"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:40:10:4010"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:40:11:4011"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:40:32:4032"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:40:58:4058"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:41:04:414"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:44:13:4413"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:44:18:4418"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:44:22:4422"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:44:22:4422"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:44:22:4422"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:49:27:4927"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:49:40:4940"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:49:44:4944"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:49:44:4944"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:49:44:4944"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:49:52:4952"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:51:26:5126"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:51:26:5126"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:51:26:5126"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:51:39:5139"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:51:40:5140"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:51:44:5144"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:51:50:5150"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:51:53:5153"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:29:5329"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:31:5331"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:31:5331"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:31:5331"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:36:5336"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:41:5341"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:41:5341"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:41:5341"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:42:5342"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:46:5346"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:48:5348"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:50:5350"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:53:5353"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:54:42:5442"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:54:48:5448"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:54:53:5453"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:54:56:5456"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:00:550"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:00:550"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:00:550"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:12:5512"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:18:5518"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:38:5538"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:41:5541"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:47:5547"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:48:5548"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:48:5548"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:48:5548"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:49:5549"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:49:5549"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:50:5550"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:50:5550"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:51:5551"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:59:5559"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:56:01:561"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:56:01:561"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:56:01:561"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:56:08:568"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:56:08:568"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:56:09:569"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:56:23:5623"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:01:07:17"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:01:13:113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:01:24:124"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:01:24:124"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:01:24:124"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:01:27:127"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:01:31:131"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:01:31:131"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:01:31:131"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:02:14:214"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:02:16:216"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-04 09:58:49:5849"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-05 21:00:57:057"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-05 21:01:02:12"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-05 21:01:03:13"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-05 21:01:03:13"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-05 21:03:02:32"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-05 21:03:05:35"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-05 21:03:05:35"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-05 21:03:05:35"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-05 21:03:17:317"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-05 21:03:17:317"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:03:23:323"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:03:37:337"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:03:37:337"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:03:46:346"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:17:517"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:17:517"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:18:518"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:22:522"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:25:525"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:26:526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:31:531"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:35:535"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:36:536"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:40:540"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:44:544"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:51:551"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:52:552"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:11:611"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:11:611"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:14:614"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:19:619"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:21:621"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:21:621"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:24:624"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:25:625"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:25:625"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:28:628"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:38:638"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:08:50:850"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:08:50:850"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:08:50:850"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:08:50:850"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:08:54:854"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:09:34:934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:09:34:934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:09:35:935"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:09:35:935"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:09:40:940"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:11:25:1125"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:11:29:1129"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:11:29:1129"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:11:29:1129"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:11:44:1144"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:11:56:1156"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:12:14:1214"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:12:31:1231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:12:49:1249"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:12:51:1251"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:12:55:1255"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:13:03:133"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:13:15:1315"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:17:26:1726"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-05 21:17:26:1726"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:17:26:1726"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:17:28:1728"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:18:13:1813"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:18:19:1819"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:18:22:1822"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:18:25:1825"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:18:26:1826"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:18:29:1829"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:18:59:1859"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:20:03:203"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:20:04:204"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:20:04:204"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:20:04:204"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:20:09:209"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:21:14:2114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:21:15:2115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:23:05:235"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:23:05:235"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:23:33:2333"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:23:33:2333"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:23:33:2333"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:24:23:2423"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:24:24:2424"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:24:28:2428"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:24:31:2431"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:25:23:2523"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:25:26:2526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:25:26:2526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:25:26:2526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:25:44:2544"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:25:58:2558"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:26:16:2616"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:26:20:2620"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:26:43:2643"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:26:49:2649"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:26:49:2649"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:26:51:2651"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:26:54:2654"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:26:56:2656"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:27:02:272"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:27:04:274"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:27:05:275"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:27:06:276"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:27:07:277"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:27:40:2740"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:27:41:2741"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:27:41:2741"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:28:14:2814"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:28:18:2818"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:31:46:3146"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:32:34:3234"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:36:18:3618"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:36:25:3625"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:36:27:3627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:36:29:3629"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:38:00:380"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:05:395"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:06:396"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:06:396"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:06:396"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:10:3910"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:12:3912"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:15:3915"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:22:3922"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:22:3922"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:22:3922"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:41:3941"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:42:3942"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:43:3943"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:43:3943"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:47:3947"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:48:3948"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:49:3949"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:50:3950"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:50:3950"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:51:3951"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:51:3951"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:58:3958"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:59:3959"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:59:3959"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:01:401"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:03:403"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:07:407"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:08:408"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:08:408"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:09:409"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:10:4010"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:11:4011"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:12:4012"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:14:4014"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:20:4020"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:42:00:420"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:42:01:421"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:42:01:421"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:42:01:421"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:32:4432"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:32:4432"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:32:4432"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:33:4433"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:34:4434"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:34:4434"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:35:4435"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:35:4435"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:35:4435"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:36:4436"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:37:4437"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:37:4437"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:37:4437"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:37:4437"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:37:4437"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:39:4439"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:39:4439"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:40:4440"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:40:4440"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:40:4440"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:40:4440"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:33:4733"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:39:4739"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:39:4739"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:40:4740"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:40:4740"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:41:4741"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:41:4741"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:42:4742"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:43:4743"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:48:4748"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:48:4748"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:48:4748"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:48:4748"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:50:4750"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:50:4750"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:50:4750"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:50:4750"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:50:4750"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:50:4750"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:56:4756"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:56:4756"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:56:4756"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:57:4757"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:57:4757"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:58:4758"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:58:4758"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:59:4759"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:59:4759"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:48:00:480"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:48:00:480"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:48:00:480"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:48:00:480"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:48:00:480"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:48:00:480"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:48:00:480"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:48:02:482"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:48:02:482"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:48:02:482"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:50:31:5031"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:50:39:5039"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:50:46:5046"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:50:47:5047"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:51:10:5110"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:51:53:5153"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:52:57:5257"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:52:57:5257"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:52:58:5258"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:53:01:531"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:53:09:539"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:53:13:5313"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:54:58:5458"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:55:00:550"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:55:01:551"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:55:03:553"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:55:08:558"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:55:44:5544"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:55:45:5545"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:55:47:5547"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:55:47:5547"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:55:47:5547"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:00:04:04"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:00:04:04"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:00:04:04"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:00:10:010"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:00:11:011"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:00:11:011"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:00:14:014"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:00:23:023"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:00:54:054"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:00:57:057"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:05:15"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:05:15"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:06:16"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:16:116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:17:117"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:17:117"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:18:118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:22:122"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:22:122"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:22:122"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:26:126"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:26:126"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:27:127"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:33:133"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:33:133"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:33:133"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:36:136"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:38:138"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:31:231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:33:233"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:34:234"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:34:234"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:35:235"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:36:236"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:36:236"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:36:236"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:37:237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:54:254"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:55:255"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:55:255"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:57:257"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:03:33"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:03:33"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:04:34"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:06:36"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:06:36"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:08:38"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:11:311"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:11:311"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:12:312"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:12:312"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:13:313"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:15:315"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:15:315"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:16:316"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:18:318"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:18:318"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:18:318"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:19:319"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:19:319"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:19:319"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:19:319"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:05:01:51"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:07:77"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:07:77"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:08:78"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:09:79"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:11:711"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:11:711"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:12:712"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:15:715"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:16:716"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:16:716"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:16:716"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:16:716"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:16:716"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:18:718"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:18:718"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:18:718"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:08:13:813"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:08:15:815"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:08:15:815"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:08:15:815"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:08:42:842"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:08:42:842"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:08:42:842"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:08:43:843"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:08:54:854"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:30:930"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:32:932"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:34:934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:34:934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:34:934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:34:1034"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:35:1035"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:35:1035"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:35:1035"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:36:1036"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:36:1036"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:36:1036"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:36:1036"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:36:1036"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:36:1036"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:36:1036"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:36:1036"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:36:1036"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:36:1036"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:39:1039"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:40:1040"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:01:111"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:02:112"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:02:112"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:02:112"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:02:112"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:02:112"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:03:113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:03:113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:03:113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:03:113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:04:114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:04:114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:04:114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:04:114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:04:114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:04:114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:04:114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:04:114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:05:115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:05:115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:05:115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:05:115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:05:115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:05:115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:06:116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:06:116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:06:116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:16:20:1620"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:16:22:1622"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:16:22:1622"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:16:22:1622"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:16:35:1635"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:16:43:1643"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:16:44:1644"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:16:45:1645"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:16:49:1649"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:16:51:1651"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:17:01:171"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:26:46:2646"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:26:47:2647"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:26:48:2648"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:26:48:2648"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:26:48:2648"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:27:11:2711"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:05:38:538"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:05:42:542"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:05:43:543"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:05:45:545"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:05:50:550"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:05:47:547"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:05:48:548"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:05:49:549"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:13:45:1345"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:13:48:1348"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:13:48:1348"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:13:48:1348"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:17:13:1713"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:17:15:1715"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:17:17:1717"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:37:23:3723"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:56:50:5650"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 00:44:19:4419"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 07:38:36:3836"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 08:45:35:4535"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 08:45:35:4535"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 08:45:35:4535"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 08:51:45:5145"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 08:51:45:5145"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 08:51:48:5148"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 08:51:48:5148"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 08:51:48:5148"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 08:51:51:5151"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:15:07:157"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:15:15:1515"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:15:15:1515"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:15:15:1515"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:15:40:1540"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:21:30:2130"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:21:30:2130"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:21:30:2130"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:21:47:2147"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:22:18:2218"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:22:19:2219"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:22:23:2223"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:22:23:2223"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:22:23:2223"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:22:27:2227"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:22:28:2228"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:22:33:2233"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:22:33:2233"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:22:33:2233"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:27:13:2713"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:27:18:2718"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:27:20:2720"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:27:20:2720"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:27:20:2720"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:27:51:2751"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:27:51:2751"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:27:55:2755"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:27:55:2755"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:27:55:2755"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:28:57:2857"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:28:57:2857"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:29:00:290"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:29:00:290"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:29:00:290"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:29:29:2929"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:29:39:2939"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:30:53:3053"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:30:53:3053"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:30:54:3054"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:30:54:3054"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:30:57:3057"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:30:57:3057"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:30:57:3057"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:30:57:3057"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:31:33:3133"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:33:52:3352"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:33:52:3352"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:33:52:3352"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:33:52:3352"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:33:53:3353"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:35:20:3520"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:35:21:3521"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:38:38:3838"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:38:43:3843"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:39:34:3934"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:40:03:403"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:40:38:4038"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:43:11:4311"}
{"email":"<EMAIL>","level":"info","message":"New user registered","timestamp":"2025-06-12 14:43:11:4311","userId":2,"username":"LisaDunn95"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:44:03:443"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-12 14:44:03:443"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:44:04:444"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:44:10:4410"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:47:18:4718"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:48:06:486"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:48:07:487"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:48:07:487"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:48:07:487"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:48:42:4842"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:48:42:4842"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:48:42:4842"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:48:43:4843"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:48:43:4843"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:48:43:4843"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:48:45:4845"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:48:45:4845"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:48:45:4845"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:49:22:4922"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:49:23:4923"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:49:23:4923"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:49:23:4923"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:49:26:4926"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:49:26:4926"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:49:26:4926"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:49:28:4928"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:49:28:4928","total":2}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:50:24:5024"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:25:5025"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:25:5025"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:26:5026"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:50:29:5029"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:50:29:5029"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:50:29:5029"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:30:5030"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:50:30:5030","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:38:5038"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:50:38:5038","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:52:5052"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:50:52:5052","total":2}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:50:57:5057"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:58:5058"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:58:5058"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:58:5058"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:59:5059"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:59:5059"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:51:02:512"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:51:02:512"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:51:02:512"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:51:04:514"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:51:04:514","total":2}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:51:07:517"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:51:16:5116"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:51:16:5116","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:51:16:5116"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:51:16:5116","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:53:11:5311"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:53:19:5319"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:53:19:5319","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:53:51:5351"}
{"adminUser":"admin","level":"info","message":"Admin accessed user details","targetUser":"LisaDunn95","timestamp":"2025-06-12 14:53:51:5351","userId":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:55:10:5510"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:55:10:5510","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:55:23:5523"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:55:27:5527"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:55:27:5527","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:55:28:5528"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:55:28:5528","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:56:02:562"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:56:07:567"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:56:07:567","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:57:42:5742"}
{"adminUser":"admin","level":"info","message":"Admin accessed user details","targetUser":"LisaDunn95","timestamp":"2025-06-12 14:57:42:5742","userId":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:57:50:5750"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:57:56:5756"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:57:56:5756","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:59:49:5949"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:59:49:5949"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:59:52:5952"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:59:52:5952"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:59:53:5953"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:59:54:5954"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:59:54:5954","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:59:58:5958"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:59:59:5959"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:00:06:06"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:00:06:06","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:00:51:051"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:00:57:057"}
{"adminUser":"admin","level":"info","message":"Admin accessed user details","targetUser":"LisaDunn95","timestamp":"2025-06-12 15:00:57:057","userId":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:01:13:113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:01:33:133"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:01:34:134"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:01:34:134","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:03:09:39"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:03:14:314"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:03:14:314","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:03:46:346"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:03:47:347"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:04:26:426"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:04:31:431"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:04:31:431","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:04:46:446"}
{"adminUser":"admin","level":"info","message":"Admin accessed user details","targetUser":"LisaDunn95","timestamp":"2025-06-12 15:04:46:446","userId":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:06:37:637"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:06:37:637"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:06:37:637","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:06:37:637"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:06:37:637","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:06:37:637"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:06:37:637","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:07:00:70"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:07:10:710"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:07:10:710","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:07:11:711"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:07:11:711","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:07:11:711"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:07:11:711","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:07:44:744"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:07:55:755"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:07:55:755","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:07:55:755"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:07:55:755","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:07:55:755"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:07:55:755","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:08:43:843"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:08:47:847"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:08:57:857"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:08:57:857","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:08:58:858"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:08:58:858","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:08:58:858"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:08:58:858","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:08:58:858"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:08:58:858","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:08:58:858"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:08:58:858","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:08:58:858"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:08:58:858","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:09:29:929"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:09:30:930"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:09:30:930","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:09:30:930"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:09:30:930","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:09:30:930"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:09:30:930","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:09:49:949"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:09:53:953"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:09:53:953","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:09:53:953"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:09:53:953","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:09:53:953"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:09:53:953","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:10:29:1029"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:10:55:1055"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:05:115"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:11:05:115","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:05:115"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:11:05:115","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:05:115"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:11:05:115","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:44:1144"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:47:1147"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:58:1158"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:11:58:1158","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:58:1158"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:11:58:1158","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:59:1159"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:11:59:1159","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:59:1159"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:11:59:1159","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:59:1159"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:11:59:1159","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:59:1159"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:11:59:1159","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:12:16:1216"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:12:37:1237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:12:49:1249"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:12:49:1249","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:18:53:1853"}
{"adminUser":"admin","level":"info","message":"Admin accessed user details","targetUser":"LisaDunn95","timestamp":"2025-06-12 15:18:53:1853","userId":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:18:58:1858"}
{"adminUser":"admin","deletedUser":"LisaDunn95","level":"info","message":"User deleted by admin","timestamp":"2025-06-12 15:18:58:1858","userId":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:18:58:1858"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:18:58:1858","total":1}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:14:59:1459"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:15:01:151"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:15:01:151"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:15:01:151"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:15:18:1518"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-26 14:15:18:1518"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:15:18:1518"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:15:23:1523"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:15:23:1523"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:15:23:1523"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:15:26:1526"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:15:26:1526","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:30:50:3050"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:30:50:3050","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:31:01:311"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:31:01:311","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:31:13:3113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:31:13:3113"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:31:13:3113","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:31:16:3116"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:31:16:3116","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:31:23:3123"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:31:23:3123","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:31:35:3135"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:31:35:3135","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:31:55:3155"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:31:55:3155","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:32:09:329"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:32:09:329","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:32:22:3222"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:32:22:3222","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:32:36:3236"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:32:36:3236","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:32:50:3250"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:32:50:3250","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:33:15:3315"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:33:15:3315","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:34:48:3448"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:34:48:3448","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:35:03:353"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:35:03:353","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:41:52:4152"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:41:55:4155"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:41:55:4155","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:43:36:4336"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:43:37:4337"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:43:37:4337","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:44:52:4452"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:44:53:4453"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:44:53:4453","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:45:46:4546"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:45:47:4547"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:45:47:4547","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:46:00:460"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:46:00:460","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:46:06:466"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:46:07:467"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:46:07:467","total":1}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:46:09:469"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:46:09:469"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:47:11:4711"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:47:11:4711"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:47:13:4713"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:47:13:4713","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:49:44:4944"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:49:45:4945"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:49:45:4945"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:49:45:4945","total":1}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:49:52:4952"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:49:53:4953"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:49:53:4953","total":1}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-06-26 14:51:11:5111"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-06-26 14:51:11:5111"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:51:11:5111"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:51:11:5111"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:51:17:5117"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:51:17:5117"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:51:17:5117"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:51:19:5119"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:51:19:5119","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:52:22:5222"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:52:23:5223"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:52:23:5223"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:52:23:5223"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:52:23:5223","total":1}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:55:20:5520"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:55:20:5520"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:55:20:5520"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:55:21:5521"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:55:21:5521"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:55:21:5521","total":1}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:56:17:5617"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:56:17:5617"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:56:17:5617"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:56:18:5618"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:56:18:5618","total":1}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:56:27:5627"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:56:27:5627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:56:27:5627"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:56:27:5627","total":1}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:56:37:5637"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:56:37:5637"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:56:58:5658"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-26 14:56:58:5658"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:56:58:5658"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:57:53:5753"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:57:53:5753"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:57:53:5753"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:57:54:5754"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:57:55:5755"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:57:55:5755","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:58:00:580"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:58:01:581"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:58:01:581"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:58:02:582"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:58:02:582","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:59:11:5911"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:59:11:5911"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:59:11:5911"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:59:12:5912"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:59:12:5912","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:00:28:028"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:00:28:028"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:00:28:028"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:00:29:029"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:00:29:029","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:00:38:038"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:00:38:038"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:00:38:038","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:00:47:047"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:00:48:048"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:00:48:048"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:00:48:048","total":0}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:00:48:048"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:06:07:67"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:06:07:67"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:06:07:67"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:06:07:67","total":0}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:06:07:67"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:06:14:614"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:06:15:615"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:06:19:619"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:06:20:620"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:06:20:620"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:06:20:620","total":0}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:06:20:620"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:08:44:844"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:08:46:846"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:08:47:847"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:08:48:848"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:08:48:848"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:08:49:849"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:08:50:850"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:08:51:851"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:08:52:852"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:08:52:852","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:09:09:99"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:09:10:910"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:09:10:910"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:10:18:1018"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:10:28:1028"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:10:40:1040"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:11:07:117"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:11:07:117"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:11:07:117","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:11:07:117"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:11:07:117","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:11:20:1120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:11:21:1121"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:11:21:1121","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:11:21:1121"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:11:21:1121","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:11:31:1131"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:11:31:1131"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:11:31:1131","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:11:31:1131"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:11:31:1131","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:11:55:1155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:12:57:1257"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:12:57:1257"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:12:57:1257"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:12:58:1258"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:05:135"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:13:05:135","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:05:135"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:05:135"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:13:05:135","total":0}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:05:135"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:06:136"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:13:06:136","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:37:1337"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:38:1338"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:39:1339"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:40:1340"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:42:1342"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:13:42:1342","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:42:1342"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:42:1342"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:13:42:1342","total":0}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:42:1342"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:43:1343"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:13:43:1343","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:45:1345"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:46:1346"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:47:1347"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:13:47:1347","total":0}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:47:1347"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:49:1549"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:49:1549"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:52:1552"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:52:1552"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:53:1553"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:54:1554"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:15:54:1554","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:54:1554"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:54:1554"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:15:54:1554","total":0}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:54:1554"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:54:1554"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:15:54:1554","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:54:1554"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:54:1554"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:15:54:1554","total":0}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:54:1554"}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-06-26 15:16:14:1614"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-06-26 15:16:14:1614"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:16:14:1614"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:17:00:170"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:17:01:171"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:17:17:1717"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:17:17:1717"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:17:17:1717","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:23:26:2326"}
{"adminUser":"admin","createdUser":"FinnWolfhard_Actual","level":"info","message":"User created by admin","role":"user","timestamp":"2025-06-26 15:23:26:2326"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:23:26:2326"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:23:26:2326","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:29:51:2951"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:29:52:2952"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:29:52:2952"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:29:55:2955"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:29:55:2955"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:29:56:2956"}
{"adminUser":"admin","level":"info","message":"Admin accessed user details","targetUser":"FinnWolfhard_Actual","timestamp":"2025-06-26 15:29:55:2955","userId":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:29:57:2957"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:29:57:2957","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:29:57:2957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:29:58:2958"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:30:01:301"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:30:01:301"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:30:01:301","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:30:01:301"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:30:01:301"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:30:01:301","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:30:01:301"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:30:01:301"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:30:01:301","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:30:01:301"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:30:02:302"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:30:02:302","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:14:514"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:14:514"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:15:515"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:16:516"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:17:517"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:18:518"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:19:519"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:05:19:519","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:20:520"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:20:520"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:05:20:520","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:20:520"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:05:20:520","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:20:520"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:20:520"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:05:20:520","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:20:520"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:20:520"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:05:20:520","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:20:520"}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-06-26 16:32:52:3252"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-06-26 16:32:52:3252"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 16:32:52:3252"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 16:32:56:3256"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 16:32:56:3256"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 16:32:56:3256"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 16:33:24:3324"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-26 16:33:24:3324"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:33:25:3325"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:03:343"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:04:344"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:04:344"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:07:347"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:07:347"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:07:347"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:34:07:347","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:07:347"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:18:3418"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:22:3422"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:22:3422"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:34:22:3422","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:23:3423"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:38:28:3828"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:38:29:3829"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:38:29:3829"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:38:30:3830"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:38:31:3831"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:38:31:3831"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:38:31:3831","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:38:31:3831"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:47:45:4745"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:47:55:4755"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:47:56:4756"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:47:56:4756","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:47:57:4757"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:53:13:5313"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:53:13:5313"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:53:15:5315"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:53:15:5315"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:53:15:5315","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:53:15:5315"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:54:46:5446"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:54:46:5446"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:54:46:5446"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:54:50:5450"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:54:50:5450"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:54:50:5450","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:54:50:5450"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:55:01:551"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:55:06:556"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:55:06:556","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:56:24:5624"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:56:24:5624"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:56:27:5627"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:56:27:5627","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:56:27:5627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:56:27:5627"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:56:27:5627","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:56:27:5627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:56:54:5654"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:56:54:5654"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:56:54:5654"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:56:54:5654","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:56:54:5654"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:57:10:5710"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:58:17:5817"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:58:17:5817"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:58:20:5820"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:58:20:5820","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:58:21:5821"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:58:21:5821"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:58:21:5821","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:58:21:5821"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:00:41:041"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:00:42:042"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:00:43:043"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:00:43:043","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:00:45:045"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:00:45:045"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:00:45:045","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:00:45:045"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:03:40:340"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:03:42:342"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:03:42:342"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:03:42:342","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:03:42:342"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:03:46:346"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:03:59:359"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:03:59:359"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:04:00:40","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:04:00:40"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:06:56"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:06:56"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:08:58"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:09:59"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:10:510"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:10:510"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:05:10:510","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:10:510"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:13:513"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:05:13:513","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:14:514"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:14:514"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:05:14:514","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:14:514"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:51:551"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:51:551"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:51:551"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:54:554"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:55:555"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:56:556"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:56:556"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:05:56:556","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:56:556"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:57:557"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:05:57:557","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:06:01:61"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:06:01:61"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:06:01:61","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:06:01:61"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:06:26:626"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:06:27:627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:06:27:627"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:06:27:627","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:06:27:627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:09:24:924"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:09:26:926"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:09:26:926"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:09:26:926","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:09:26:926"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:09:53:953"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:09:56:956"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:09:57:957"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:09:57:957","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:01:111"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:06:116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:06:116"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:11:06:116","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:06:116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:53:1153"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:53:1153"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:55:1155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:55:1155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:11:57:1157","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:11:57:1157","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:11:57:1157","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:11:57:1157","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:12:29:1229"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:12:31:1231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:12:31:1231"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:12:31:1231","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:12:31:1231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:19:19:1919"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:19:24:1924"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:19:24:1924"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:19:24:1924","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:19:24:1924"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:20:54:2054"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:20:59:2059"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:20:59:2059"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:20:59:2059","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:20:59:2059"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:21:54:2154"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:21:55:2155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:21:56:2156"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:21:56:2156","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:21:56:2156"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:26:20:2620"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:27:54:2754"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:28:01:281"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:29:42:2942"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:29:43:2943"}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-06-26 17:31:50:3150"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-06-26 17:31:50:3150"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:31:50:3150"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:32:31:3231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:32:34:3234"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:32:46:3246"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:33:05:335"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:33:07:337"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:33:07:337","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:33:08:338"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:33:34:3334"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:33:36:3336"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:33:37:3337","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:33:38:3338"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:34:02:342"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:34:08:348"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:34:08:348"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:34:08:348","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:34:08:348"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:36:13:3613"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:36:18:3618"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:36:18:3618"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:36:18:3618","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:36:18:3618"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:37:08:378"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:37:14:3714"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:37:14:3714"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:37:14:3714","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:37:14:3714"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:37:41:3741"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:37:43:3743"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:37:43:3743"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:37:43:3743","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:37:43:3743"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:37:51:3751"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:38:04:384"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:38:05:385"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:38:05:385","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:38:05:385"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:40:05:405"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:40:08:408"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:40:08:408"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:40:08:408","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:40:08:408"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:41:17:4117"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:41:20:4120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:41:20:4120"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:41:20:4120","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:41:20:4120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:49:00:490"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:49:07:497"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:49:07:497"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:49:07:497","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:49:07:497"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 20:35:21:3521"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-27 08:39:15:3915"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-27 08:39:18:3918"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-27 08:39:18:3918"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-27 08:39:18:3918","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-27 08:39:19:3919"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-27 09:21:12:2112"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-27 09:21:12:2112","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-27 09:31:44:3144"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-27 09:31:44:3144"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-27 11:14:25:1425"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 13:58:29:5829"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 13:58:39:5839"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 13:58:39:5839","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 13:58:48:5848"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 13:58:49:5849"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 13:58:49:5849","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 13:59:22:5922"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:00:04:04"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:00:51:051"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:00:51:051"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:00:51:051","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:00:53:053"}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-07-03 14:25:45:2545"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-07-03 14:25:45:2545"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:25:45:2545"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:25:55:2555"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:28:16:2816"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:31:52:3152"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:32:03:323"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:34:58:3458"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:35:02:352"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:35:05:355"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:35:05:355"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:35:05:355","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:35:33:3533"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:35:34:3534"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:35:34:3534","total":2}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-07-03 14:35:35:3535"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-07-03 14:35:35:3535"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:35:35:3535"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:35:37:3537"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:35:37:3537"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:35:37:3537"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:35:53:3553"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-07-03 14:35:53:3553"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:35:53:3553"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:35:55:3555"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:35:56:3556"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:36:08:368"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:36:08:368"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:36:09:369"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:36:26:3626"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:36:27:3627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:36:27:3627"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:36:27:3627","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:36:27:3627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:36:28:3628"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:36:28:3628"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:36:28:3628"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:36:28:3628","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:36:28:3628"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:36:29:3629"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:36:29:3629"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:36:29:3629"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:36:29:3629","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:36:29:3629"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:53:3753"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:53:3753"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:53:3753"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:53:3753"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:54:3754"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:54:3754"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:54:3754"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:55:3755"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:37:55:3755","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:55:3755"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:55:3755"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:55:3755"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:37:55:3755","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:55:3755"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:55:3755"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:37:55:3755","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:55:3755"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:55:3755"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:37:55:3755","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:55:3755"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:55:3755"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:55:3755"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:55:3755"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:55:3755"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:37:55:3755","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:55:3755"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:37:55:3755","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:55:3755"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:55:3755"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:58:3758"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:37:58:3758","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:37:58:3758"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:45:4045"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:45:4045"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:45:4045"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:45:4045"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:46:4046"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:46:4046"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:46:4046"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:46:4046"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:46:4046"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:40:46:4046","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:46:4046"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:46:4046"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:46:4046"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:40:46:4046","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:46:4046"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:46:4046"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:46:4046"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:46:4046"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:40:46:4046","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:46:4046"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:40:46:4046","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:46:4046"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:46:4046"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:46:4046"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:46:4046"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:40:46:4046","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:46:4046"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:47:4047"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:40:47:4047","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:47:4047"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:40:47:4047","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:40:47:4047"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:16:4216"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:16:4216"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:16:4216"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:16:4216"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:17:4217"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:17:4217"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:17:4217"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:17:4217"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:17:4217"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:42:17:4217","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:17:4217"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:17:4217"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:17:4217"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:42:17:4217","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:17:4217"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:17:4217"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:42:17:4217","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:17:4217"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:17:4217"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:17:4217"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:17:4217"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:42:17:4217","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:17:4217"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:42:17:4217","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:17:4217"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:17:4217"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:17:4217"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:42:17:4217","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:17:4217"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:42:18:4218","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:18:4218"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:18:4218"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:18:4218"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:18:4218"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:19:4219"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:42:19:4219","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:19:4219"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:42:19:4219","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:19:4219"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:20:4220"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:20:4220"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:20:4220"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:20:4220"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:20:4220"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:21:4221"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:21:4221"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:21:4221"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:42:21:4221","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:21:4221"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:42:21:4221","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:21:4221"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:21:4221"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:21:4221"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:21:4221"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:21:4221"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:21:4221"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:42:21:4221","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:21:4221"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:21:4221"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:42:21:4221","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:21:4221"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:21:4221"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:42:21:4221","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:21:4221"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:21:4221"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:21:4221"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:42:21:4221","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:33:4233"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:42:33:4233","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:33:4233"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:45:4245"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:45:4245"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:42:45:4245","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:54:4254"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:42:54:4254","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:42:54:4254"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:43:21:4321"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:43:21:4321","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:43:21:4321"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:43:33:4333"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:43:33:4333","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:43:33:4333"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:43:44:4344"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:43:44:4344","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:43:44:4344"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:43:58:4358"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:43:58:4358","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:43:58:4358"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:44:32:4432"}
{"level":"info","message":"SIGINT received, stopping content scheduler","timestamp":"2025-07-03 14:44:44:4444"}
{"level":"info","message":"Content scheduler stopped","timestamp":"2025-07-03 14:44:44:4444"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:46:42:4642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:46:44:4644"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:46:44:4644","total":2}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-07-03 14:46:48:4648"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-07-03 14:46:48:4648"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:46:48:4648"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:46:50:4650"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:46:50:4650"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:46:50:4650"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:47:06:476"}
{"email":"<EMAIL>","level":"warn","message":"Login failed: Invalid password","timestamp":"2025-07-03 14:47:06:476"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:48:25:4825"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:48:27:4827"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:48:27:4827","total":2}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-07-03 14:48:27:4827"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-07-03 14:48:27:4827"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:48:27:4827"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:48:28:4828"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:48:28:4828"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:48:29:4829"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:48:41:4841"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-07-03 14:48:41:4841"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:48:41:4841"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:48:43:4843"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:48:46:4846"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:48:47:4847"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:48:47:4847"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:48:47:4847","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:49:53:4953"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:49:55:4955"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:49:55:4955","total":2}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-07-03 14:51:19:5119"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-07-03 14:51:19:5119"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:51:19:5119"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:51:22:5122"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:51:22:5122","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:52:25:5225"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:52:26:5226"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:52:26:5226","total":2}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-07-03 14:52:41:5241"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-07-03 14:52:41:5241"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:52:41:5241"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:52:42:5242"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:52:42:5242"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:52:43:5243"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:52:47:5247"}
{"level":"info","message":"SIGINT received, stopping content scheduler","timestamp":"2025-07-03 14:53:46:5346"}
{"level":"warn","message":"Content scheduler is not running","timestamp":"2025-07-03 14:53:46:5346"}
{"level":"info","message":"SIGINT received, stopping content scheduler","timestamp":"2025-07-03 14:53:46:5346"}
{"level":"info","message":"Content scheduler stopped","timestamp":"2025-07-03 14:53:46:5346"}
{"level":"info","message":"SIGINT received, stopping content scheduler","timestamp":"2025-07-03 14:56:10:5610"}
{"level":"info","message":"SIGINT received, stopping content scheduler","timestamp":"2025-07-03 14:56:10:5610"}
{"level":"info","message":"Content scheduler stopped","timestamp":"2025-07-03 14:56:10:5610"}
{"level":"info","message":"SIGINT received, stopping content scheduler","timestamp":"2025-07-03 14:56:10:5610"}
{"level":"info","message":"Content scheduler stopped","timestamp":"2025-07-03 14:56:10:5610"}
{"level":"info","message":"Content scheduler stopped","timestamp":"2025-07-03 14:56:10:5610"}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-07-03 14:56:43:5643"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-07-03 14:56:43:5643"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:56:43:5643"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:56:45:5645"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:56:45:5645"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:56:45:5645"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:56:54:5654"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-07-03 14:56:54:5654"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:56:54:5654"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:56:56:5656"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:56:56:5656"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:56:57:5657"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 14:56:57:5657"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 14:56:57:5657","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:01:52:152"}
{"adminUser":"admin","contentId":5,"contentType":"news","level":"info","message":"Content posted as user","published":true,"targetUser":"FinnWolfhard_Actual","timestamp":"2025-07-03 15:01:52:152"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:01:55:155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:01:55:155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:01:57:157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:01:57:157"}
{"level":"info","message":"AI usage metrics requested","requestedBy":"admin","timeRange":"24h","timestamp":"2025-07-03 15:01:57:157","totalGenerations":0}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:01:57:157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:02:02:22"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:02:05:25"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:02:09:29"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:02:18:218"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:02:18:218"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:02:18:218"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:02:18:218"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:02:25:225"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:02:25:225"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:05:04:54"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:05:04:54"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:05:04:54"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:05:04:54"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:05:46:546"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:05:48:548"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:05:56:556"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:05:59:559"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:05:59:559"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:06:00:60"}
{"level":"info","message":"AI usage metrics requested","requestedBy":"admin","timeRange":"24h","timestamp":"2025-07-03 15:06:00:60","totalGenerations":0}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:06:00:60"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:06:01:61"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:06:01:61"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 15:06:01:61","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:06:01:61"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:06:03:63"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:06:07:67"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:06:07:67"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:06:08:68"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:06:08:68"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:06:11:611"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:06:11:611"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:06:14:614"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:06:14:614"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:06:16:616"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:06:20:620"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:06:20:620"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:06:21:621"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:06:22:622"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:06:24:624"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:06:25:625"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:09:23:923"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:09:25:925"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:09:25:925"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:09:26:926"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:09:26:926"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:09:26:926"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:09:26:926"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:09:27:927"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:09:27:927"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:09:27:927"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:09:27:927"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-03 15:09:27:927","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:09:27:927"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:09:27:927"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:09:29:929"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:09:29:929"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:09:29:929"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:09:29:929"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:12:57:1257"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:14:36:1436"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-03 15:16:54:1654"}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-07-09 12:47:44:4744"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-07-09 12:47:44:4744"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:47:44:4744"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:47:48:4748"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:47:49:4749"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:47:49:4749"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:47:49:4749"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:47:49:4749"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:47:52:4752"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:48:08:488"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:48:22:4822"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:48:22:4822"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:48:22:4822"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:48:22:4822"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:48:37:4837"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:48:38:4838"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:48:38:4838"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:48:38:4838"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:48:38:4838"}
{"level":"info","message":"SIGINT received, stopping content scheduler","timestamp":"2025-07-09 12:50:44:5044"}
{"level":"info","message":"Content scheduler stopped","timestamp":"2025-07-09 12:50:44:5044"}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-07-09 12:50:55:5055"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-07-09 12:50:55:5055"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:50:55:5055"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:50:58:5058"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:50:58:5058"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:50:58:5058"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:50:58:5058"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:51:10:5110"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:51:11:5111"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:51:11:5111"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:51:11:5111"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:51:11:5111"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:51:36:5136"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:51:37:5137"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:51:37:5137"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:51:37:5137"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:51:37:5137"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:53:51:5351"}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-07-09 12:54:02:542"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-07-09 12:54:02:542"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:54:02:542"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:54:12:5412"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:54:32:5432"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:54:38:5438"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:54:38:5438"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:54:38:5438"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:54:49:5449"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:54:51:5451"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:54:56:5456"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:54:56:5456"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:54:56:5456"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:54:59:5459"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:55:06:556"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:55:19:5519"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:55:19:5519"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:55:21:5521"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:55:21:5521"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:55:21:5521"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:57:56:5756"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:58:17:5817"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:58:17:5817"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:58:17:5817"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:58:54:5854"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-07-09 12:58:54:5854"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:58:54:5854"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:58:56:5856"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:59:06:596"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:59:06:596"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:59:06:596"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-09 12:59:06:596","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:59:06:596"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:59:16:5916"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:59:19:5919"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:59:21:5921"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:59:22:5922"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:59:22:5922"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:59:23:5923"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:59:24:5924"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:59:24:5924"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-09 12:59:24:5924","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:59:24:5924"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:59:28:5928"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:59:31:5931"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:59:31:5931"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-09 12:59:31:5931","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 12:59:31:5931"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:06:18:618"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:06:22:622"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:06:22:622"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-09 13:06:22:622","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:06:22:622"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:06:41:641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:06:43:643"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:06:43:643"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-09 13:06:43:643","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:06:43:643"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:08:38:838"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:08:39:839"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:08:39:839"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-09 13:08:39:839","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:08:39:839"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:10:14:1014"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:10:17:1017"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:10:17:1017"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-09 13:10:17:1017","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:10:17:1017"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:16:13:1613"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:16:17:1617"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:16:17:1617"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-09 13:16:17:1617","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:16:17:1617"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:16:56:1656"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:16:58:1658"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:16:58:1658"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-09 13:16:58:1658","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:16:58:1658"}
{"level":"info","message":"SIGINT received, stopping content scheduler","timestamp":"2025-07-09 13:17:03:173"}
{"level":"info","message":"Content scheduler stopped","timestamp":"2025-07-09 13:17:03:173"}
{"level":"info","message":"SIGINT received, stopping content scheduler","timestamp":"2025-07-09 13:17:08:178"}
{"level":"info","message":"Content scheduler stopped","timestamp":"2025-07-09 13:17:08:178"}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-07-09 13:19:23:1923"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-07-09 13:19:23:1923"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:19:23:1923"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:19:26:1926"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:19:26:1926"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:19:26:1926"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:20:09:209"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:20:10:2010"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:20:10:2010"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:20:10:2010"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:20:38:2038"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:20:39:2039"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:20:39:2039"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:20:39:2039"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:20:42:2042"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-09 13:21:14:2114"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-09 13:21:15:2115"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-09 13:21:15:2115"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-09 13:21:15:2115"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-09 13:21:26:2126"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-09 13:21:27:2127"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-09 13:21:27:2127"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-09 13:21:27:2127"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-09 13:21:59:2159"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-09 13:21:59:2159"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:23:52:2352"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-09 13:23:52:2352"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:23:53:2353"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-09 13:23:54:2354"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-09 13:23:54:2354"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-09 13:23:54:2354"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-09 13:23:54:2354"}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-07-09 13:24:19:2419"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-07-09 13:24:19:2419"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-09 13:24:19:2419"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:24:23:2423"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:24:23:2423"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:24:23:2423"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:24:31:2431"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:24:33:2433"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:24:33:2433"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:24:33:2433"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:24:37:2437"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:24:38:2438"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:24:38:2438"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:24:38:2438"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:25:15:2515"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-09 13:25:15:2515"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:25:16:2516"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-09 13:25:16:2516"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-09 13:25:16:2516"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-09 13:25:16:2516"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:25:17:2517"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:25:17:2517"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:25:17:2517"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:25:37:2537"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:25:50:2550"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:26:03:263"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:26:16:2616"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:26:43:2643"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:26:56:2656"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:27:13:2713"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:27:27:2727"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:28:25:2825"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:28:29:2829"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:28:29:2829"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:28:29:2829"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:29:00:290"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:29:10:2910"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:29:15:2915"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:29:40:2940"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:30:19:3019"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:30:23:3023"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:30:23:3023"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:30:23:3023"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:30:49:3049"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:30:50:3050"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:30:50:3050"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-09 13:30:50:3050"}
{"level":"info","message":"SIGINT received, stopping content scheduler","timestamp":"2025-07-09 13:31:05:315"}
{"level":"info","message":"Content scheduler stopped","timestamp":"2025-07-09 13:31:05:315"}
{"level":"info","message":"SIGINT received, stopping content scheduler","timestamp":"2025-07-09 13:31:22:3122"}
{"level":"info","message":"Content scheduler stopped","timestamp":"2025-07-09 13:31:22:3122"}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-07-10 14:00:56:056"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-07-10 14:00:56:056"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:00:57:057"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:01:38:138"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:01:43:143"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:01:43:143"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:01:43:143"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:01:56:156"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:02:21:221"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:03:44:344"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:08:04:84"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:08:05:85"}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-07-10 14:08:29:829"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-07-10 14:08:29:829"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:08:29:829"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:08:48:848"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:08:48:848"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:08:48:848"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:09:09:99"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:09:09:99"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:09:09:99"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-10 14:09:28:928"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-10 14:09:31:931"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-10 14:09:31:931"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-10 14:09:31:931"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:09:34:934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:10:04:104"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:10:06:106"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:10:06:106"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:10:06:106"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-10 14:10:34:1034"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:10:34:1034"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-10 14:10:34:1034"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:10:34:1034"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-10 14:10:34:1034"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:10:34:1034"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-10 14:10:53:1053"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-10 14:10:54:1054"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-10 14:10:54:1054"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-10 14:10:54:1054"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:11:55:1155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:12:00:120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:12:01:121"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:12:01:121"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:12:07:127"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:12:10:1210"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:12:10:1210"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:12:10:1210"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:12:24:1224"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:12:27:1227"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:12:27:1227"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:12:27:1227"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:12:28:1228"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:12:28:1228"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:12:39:1239"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:12:39:1239"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:12:39:1239"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:12:58:1258"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:12:59:1259"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:16:20:1620"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:16:20:1620"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:16:20:1620"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:16:20:1620"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:16:25:1625"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:16:29:1629"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:16:29:1629"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:16:29:1629"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:18:11:1811"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:18:12:1812"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:18:15:1815"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:18:27:1827"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:18:32:1832"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:21:11:2111"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:21:11:2111"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:21:16:2116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:22:12:2212"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:22:14:2214"}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-07-10 14:22:41:2241"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-07-10 14:22:41:2241"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:22:42:2242"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:23:33:2333"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:23:38:2338"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:23:38:2338"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:23:38:2338"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:24:03:243"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-07-10 14:24:03:243"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:24:03:243"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:24:05:245"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:24:14:2414"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:24:19:2419"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:24:19:2419"}
{"level":"error","message":"Error fetching audit logs: Alias \"users\" is already used in this query","stack":"Error: Alias \"users\" is already used in this query\n    at SQLiteSelectBase.leftJoin (file:///C:/Users/<USER>/OneDrive/Documents/GitHub/FWFC2/node_modules/drizzle-orm/sqlite-core/query-builders/select.js:91:15)\n    at GET (C:/Users/<USER>/OneDrive/Documents/GitHub/FWFC2/src/routes/api/admin/audit-logs/+server.ts:76:199)\n    at eval (C:/Users/<USER>/OneDrive/Documents/GitHub/FWFC2/node_modules/@sveltejs/kit/src/runtime/server/endpoint.js:49:4)\n    at AsyncLocalStorage.run (node:internal/async_local_storage/async_hooks:91:14)\n    at with_event (C:/Users/<USER>/OneDrive/Documents/GitHub/FWFC2/node_modules/@sveltejs/kit/src/runtime/app/server/event.js:53:20)\n    at render_endpoint (C:/Users/<USER>/OneDrive/Documents/GitHub/FWFC2/node_modules/@sveltejs/kit/src/runtime/server/endpoint.js:48:62)\n    at resolve (C:/Users/<USER>/OneDrive/Documents/GitHub/FWFC2/node_modules/@sveltejs/kit/src/runtime/server/respond.js:378:69)\n    at async handleErrors (C:/Users/<USER>/OneDrive/Documents/GitHub/FWFC2/src/hooks.server.ts:87:22)\n    at async handleAccessibility (C:/Users/<USER>/OneDrive/Documents/GitHub/FWFC2/src/hooks.server.ts:74:20)\n    at async respond (C:/Users/<USER>/OneDrive/Documents/GitHub/FWFC2/node_modules/@sveltejs/kit/src/runtime/server/respond.js:263:22)","timestamp":"2025-07-10 14:24:19:2419"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:24:26:2426"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:24:27:2427"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:24:27:2427"}
{"level":"error","message":"Error fetching audit logs: Alias \"users\" is already used in this query","stack":"Error: Alias \"users\" is already used in this query\n    at SQLiteSelectBase.leftJoin (file:///C:/Users/<USER>/OneDrive/Documents/GitHub/FWFC2/node_modules/drizzle-orm/sqlite-core/query-builders/select.js:91:15)\n    at GET (C:/Users/<USER>/OneDrive/Documents/GitHub/FWFC2/src/routes/api/admin/audit-logs/+server.ts:76:199)\n    at eval (C:/Users/<USER>/OneDrive/Documents/GitHub/FWFC2/node_modules/@sveltejs/kit/src/runtime/server/endpoint.js:49:4)\n    at AsyncLocalStorage.run (node:internal/async_local_storage/async_hooks:91:14)\n    at with_event (C:/Users/<USER>/OneDrive/Documents/GitHub/FWFC2/node_modules/@sveltejs/kit/src/runtime/app/server/event.js:53:20)\n    at render_endpoint (C:/Users/<USER>/OneDrive/Documents/GitHub/FWFC2/node_modules/@sveltejs/kit/src/runtime/server/endpoint.js:48:62)\n    at resolve (C:/Users/<USER>/OneDrive/Documents/GitHub/FWFC2/node_modules/@sveltejs/kit/src/runtime/server/respond.js:378:69)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async handleErrors (C:/Users/<USER>/OneDrive/Documents/GitHub/FWFC2/src/hooks.server.ts:87:22)\n    at async handleAccessibility (C:/Users/<USER>/OneDrive/Documents/GitHub/FWFC2/src/hooks.server.ts:74:20)","timestamp":"2025-07-10 14:24:27:2427"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:24:28:2428"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:24:28:2428"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:24:28:2428"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-10 14:24:28:2428","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:24:28:2428"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:24:34:2434"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:24:37:2437"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:25:09:259"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:25:12:2512"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:25:15:2515"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:25:18:2518"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:25:19:2519"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:25:20:2520"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:25:21:2521"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:25:22:2522"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:25:22:2522"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:25:23:2523"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:25:24:2524"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:25:24:2524"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:25:25:2525"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:25:25:2525"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:25:26:2526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:25:28:2528"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:25:30:2530"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:01:261"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-10 14:26:05:265"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-10 14:26:06:266"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-10 14:26:06:266"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-10 14:26:06:266"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:08:268"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:09:269"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:11:2611"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:13:2613"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:14:2614"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:16:2616"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:23:2623"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:24:2624"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:24:2624"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:25:2625"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-10 14:26:25:2625","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:25:2625"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:26:2626"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-10 14:26:26:2626","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:26:2626"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:28:2628"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:30:2630"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:31:2631"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:31:2631"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:32:2632"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:33:2633"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:34:2634"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-07-10 14:26:34:2634","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:34:2634"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:36:2636"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:39:2639"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:41:2641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:43:2643"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:45:2645"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:45:2645"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:45:2645"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:45:2645"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:47:2647"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:26:48:2648"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:37:18:3718"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-10 14:37:19:3719"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:37:20:3720"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:37:21:3721"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:37:22:3722"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:37:25:3725"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-10 14:37:28:3728"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-10 14:37:28:3728"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-07-10 14:37:28:3728"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-07-10 14:37:29:3729"}
