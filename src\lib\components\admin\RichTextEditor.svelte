<script>
	import { onMount, createEventDispatcher } from 'svelte';

	const dispatch = createEventDispatcher();

	// Props
	export let value = '';
	export let placeholder = 'Enter content...';
	export let disabled = false;
	export let height = 400;

	// State
	/** @type {HTMLElement} */
	let editorContainer;
	let isReady = false;

	// Create simple rich text editor with basic formatting
	function createSimpleEditor() {
		if (!editorContainer || isReady) {
			console.log('Cannot create simple editor: container missing or already ready');
			return;
		}

		console.log('Creating simple rich text editor...');
		editorContainer.innerHTML = '';

		// Create toolbar
		const toolbar = document.createElement('div');
		toolbar.className = 'simple-toolbar';
		toolbar.innerHTML = `
			<button type="button" data-command="bold" title="Bold"><strong>B</strong></button>
			<button type="button" data-command="italic" title="Italic"><em>I</em></button>
			<button type="button" data-command="underline" title="Underline"><u>U</u></button>
			<button type="button" data-command="insertUnorderedList" title="Bullet List">• List</button>
			<button type="button" data-command="insertOrderedList" title="Numbered List">1. List</button>
			<button type="button" data-command="createLink" title="Link">🔗</button>
		`;

		// Create contenteditable div
		const editor = document.createElement('div');
		editor.contentEditable = 'true';
		editor.innerHTML = value || '';
		editor.style.cssText = `
			min-height: ${height - 50}px;
			padding: 0.75rem;
			border: 1px solid var(--color-border-primary);
			border-top: none;
			border-radius: 0 0 4px 4px;
			font-family: inherit;
			font-size: 1rem;
			line-height: 1.5;
			outline: none;
			background-color: var(--color-surface-primary);
			color: var(--color-text-primary);
			transition: var(--transition-theme);
		`;

		if (placeholder && !value) {
			editor.setAttribute('data-placeholder', placeholder);
		}

		// Handle toolbar clicks with better error handling
		toolbar.addEventListener('click', (e) => {
			e.preventDefault();
			const target = /** @type {HTMLElement} */ (e.target);
			const button = target && target.closest ? target.closest('button') : null;
			if (!button) return;

			const command = button.dataset.command;
			if (!command) return;

			try {
				if (command === 'createLink') {
					const url = prompt('Enter URL:');
					if (url) {
						document.execCommand(command, false, url);
					}
				} else {
					document.execCommand(command, false, '');
				}
				editor.focus();
			} catch (error) {
				console.warn('Command execution failed:', command, error);
			}
		});

		// Handle content changes
		editor.addEventListener('input', () => {
			value = editor.innerHTML;
			dispatch('change', { content: value });
		});

		// Handle paste to clean up formatting
		editor.addEventListener('paste', (e) => {
			e.preventDefault();
			const clipboardData = e.clipboardData;
			if (clipboardData) {
				const text = clipboardData.getData('text/plain');
				try {
					document.execCommand('insertText', false, text);
				} catch (error) {
					// Fallback for browsers that don't support execCommand
					const selection = window.getSelection();
					if (selection && selection.rangeCount > 0) {
						const range = selection.getRangeAt(0);
						range.deleteContents();
						range.insertNode(document.createTextNode(text));
						range.collapse(false);
						selection.removeAllRanges();
						selection.addRange(range);
					}
				}
			}
		});

		editorContainer.appendChild(toolbar);
		editorContainer.appendChild(editor);

		// Set ready state
		isReady = true;
		console.log('Simple rich text editor created successfully');

		// Focus the editor
		setTimeout(() => {
			editor.focus();
		}, 100);
	}

	// Update content when value prop changes
	$: if (isReady && editorContainer) {
		const editor = editorContainer.querySelector('[contenteditable]');
		if (editor && editor.innerHTML !== value) {
			editor.innerHTML = value || '';
		}
	}

	onMount(() => {
		// For now, skip TinyMCE and go directly to simple editor for reliability
		console.log('Initializing simple rich text editor...');
		setTimeout(() => {
			if (editorContainer) {
				createSimpleEditor();
			} else {
				console.error('Editor container not found, retrying...');
				setTimeout(() => {
					if (editorContainer) {
						createSimpleEditor();
					} else {
						console.error('Editor container still not found after retry');
					}
				}, 100);
			}
		}, 50);
	});
</script>

<div class="rich-text-editor">
	<div bind:this={editorContainer} class="editor-container"></div>
	
	{#if !isReady}
		<div class="loading">
			<p>Loading editor...</p>
		</div>
	{/if}
</div>

<style>
	.rich-text-editor {
		position: relative;
		width: 100%;
	}

	.editor-container {
		width: 100%;
	}

	.loading {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: var(--color-bg-overlay);
		border: 1px solid var(--color-border-primary);
		border-radius: 4px;
		min-height: 200px;
	}

	.loading p {
		margin: 0;
		color: var(--color-text-secondary);
	}

	/* Styles for simple editor */
	:global(.simple-toolbar) {
		display: flex;
		gap: 0.5rem;
		padding: 0.5rem;
		background-color: var(--color-surface-secondary);
		border: 1px solid var(--color-border-primary);
		border-radius: 4px 4px 0 0;
		border-bottom: none;
	}

	:global(.simple-toolbar button) {
		padding: 0.25rem 0.5rem;
		border: 1px solid var(--color-border-secondary);
		border-radius: 3px;
		background-color: var(--color-surface-primary);
		color: var(--color-text-primary);
		cursor: pointer;
		font-size: 0.9rem;
		transition: var(--transition-theme);
	}

	:global(.simple-toolbar button:hover) {
		background-color: var(--color-surface-tertiary);
	}

	:global(.simple-toolbar button:active) {
		background-color: var(--color-surface-quaternary);
	}

	:global([contenteditable="true"]:empty:before) {
		content: attr(data-placeholder);
		color: var(--color-text-muted);
		font-style: italic;
	}

	:global([contenteditable="true"]:focus) {
		outline: none;
		box-shadow: 0 0 0 2px var(--color-shadow-focus);
	}

	/* Global styles for TinyMCE */
	:global(.tox-tinymce) {
		border: 1px solid var(--color-border-primary) !important;
		border-radius: 4px !important;
	}

	:global(.tox-toolbar) {
		border-bottom: 1px solid var(--color-border-primary) !important;
		background-color: var(--color-surface-secondary) !important;
	}

	:global(.tox-edit-area) {
		border: none !important;
		background-color: var(--color-surface-primary) !important;
	}

	:global(.tox-edit-area iframe) {
		background-color: var(--color-surface-primary) !important;
	}
</style>
